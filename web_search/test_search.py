#!/usr/bin/env python3
"""
Test script for the web search system
"""

import os
import sys
import json
import time
from typing import Dict, Any

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_environment():
    """Test if environment variables are set correctly"""
    print("=== ENVIRONMENT TEST ===")
    
    required_vars = ['GOOGLE_API_KEY', 'GOOGLE_CSE_ID', 'OPENAI_API_KEY']
    missing_vars = []
    
    for var in required_vars:
        value = os.getenv(var)
        if value:
            print(f"✓ {var}: {'*' * min(len(value), 10)}...")
        else:
            print(f"✗ {var}: Not set")
            missing_vars.append(var)
    
    if missing_vars:
        print(f"\nMissing environment variables: {missing_vars}")
        print("Please set these variables before running the tests.")
        return False
    
    print("✓ All environment variables are set")
    return True

def test_imports():
    """Test if all required modules can be imported"""
    print("\n=== IMPORT TEST ===")
    
    modules_to_test = [
        ('requests', 'requests'),
        ('beautifulsoup4', 'bs4'),
        ('openai', 'openai'),
        ('brotli', 'brotli'),
        ('python-dotenv', 'dotenv')
    ]
    
    failed_imports = []
    
    for package_name, import_name in modules_to_test:
        try:
            __import__(import_name)
            print(f"✓ {package_name}")
        except ImportError as e:
            print(f"✗ {package_name}: {e}")
            failed_imports.append(package_name)
    
    if failed_imports:
        print(f"\nFailed imports: {failed_imports}")
        print("Please install missing packages with: pip install -r requirements.txt")
        return False
    
    print("✓ All required modules imported successfully")
    return True

def test_google_search_api():
    """Test Google Custom Search API"""
    print("\n=== GOOGLE SEARCH API TEST ===")
    
    try:
        from browser_tool import GoogleSearchAPI
        
        api = GoogleSearchAPI()
        results = api.search("test query", num_results=3)
        
        if results:
            print(f"✓ Google Search API working - found {len(results)} results")
            print(f"  First result: {results[0].get('title', 'No title')}")
            return True
        else:
            print("✗ Google Search API returned no results")
            return False
            
    except Exception as e:
        print(f"✗ Google Search API error: {e}")
        return False

def test_content_extraction():
    """Test web content extraction"""
    print("\n=== CONTENT EXTRACTION TEST ===")
    
    try:
        from browser_tool import WebContentExtractor
        
        extractor = WebContentExtractor()
        
        # Test with a reliable website
        test_url = "https://en.wikipedia.org/wiki/Artificial_intelligence"
        content = extractor.extract_content(test_url)
        
        if content and len(content) > 100 and not content.startswith("Error"):
            print(f"✓ Content extraction working - extracted {len(content)} characters")
            print(f"  Preview: {content[:100]}...")
            return True
        else:
            print(f"✗ Content extraction failed: {content[:100] if content else 'No content'}")
            return False
            
    except Exception as e:
        print(f"✗ Content extraction error: {e}")
        return False

def test_openai_integration():
    """Test OpenAI API integration"""
    print("\n=== OPENAI API TEST ===")
    
    try:
        from openai import OpenAI
        
        client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        
        # Simple test request
        response = client.chat.completions.create(
            model="gpt-3.5-turbo",  # Use cheaper model for testing
            messages=[
                {"role": "user", "content": "Say 'API test successful' if you can read this."}
            ],
            max_tokens=10
        )
        
        if response.choices[0].message.content:
            print(f"✓ OpenAI API working")
            print(f"  Response: {response.choices[0].message.content}")
            return True
        else:
            print("✗ OpenAI API returned empty response")
            return False
            
    except Exception as e:
        print(f"✗ OpenAI API error: {e}")
        return False

def test_basic_search():
    """Test basic search functionality"""
    print("\n=== BASIC SEARCH TEST ===")
    
    try:
        from browser_tool import perform_web_search_with_ai
        
        question = "What is the capital of France?"
        result = perform_web_search_with_ai(question, "quick")
        
        if result and result.get('answer'):
            print(f"✓ Basic search working")
            print(f"  Question: {question}")
            print(f"  Answer: {result['answer'][:100]}...")
            print(f"  Sources: {len(result.get('sources', []))}")
            return True
        else:
            print("✗ Basic search failed - no answer generated")
            return False
            
    except Exception as e:
        print(f"✗ Basic search error: {e}")
        return False

def test_advanced_features():
    """Test advanced features"""
    print("\n=== ADVANCED FEATURES TEST ===")
    
    try:
        from advanced_search_utils import (
            SearchQueryOptimizer, 
            ContentAnalyzer, 
            is_question_answerable
        )
        
        # Test query optimization
        optimizer = SearchQueryOptimizer()
        optimized = optimizer.optimize_query("artificial intelligence machine learning")
        print(f"✓ Query optimization: {optimized}")
        
        # Test content analysis
        analyzer = ContentAnalyzer()
        sample_text = "In 2024, AI technology advanced by 50%. The market reached $100 billion."
        facts = analyzer.extract_key_facts(sample_text)
        print(f"✓ Content analysis: {facts}")
        
        # Test question validation
        answerable, reason = is_question_answerable("What is machine learning?")
        print(f"✓ Question validation: {answerable}")
        
        return True
        
    except Exception as e:
        print(f"✗ Advanced features error: {e}")
        return False

def run_performance_test():
    """Run a simple performance test"""
    print("\n=== PERFORMANCE TEST ===")
    
    try:
        from browser_tool import perform_web_search_with_ai
        
        questions = [
            "What is Python programming?",
            "How does photosynthesis work?",
            "What is quantum computing?"
        ]
        
        start_time = time.time()
        
        for question in questions:
            result = perform_web_search_with_ai(question, "quick")
            if not result.get('answer'):
                print(f"✗ Failed to answer: {question}")
                return False
        
        end_time = time.time()
        total_time = end_time - start_time
        avg_time = total_time / len(questions)
        
        print(f"✓ Performance test completed")
        print(f"  Total time: {total_time:.2f} seconds")
        print(f"  Average time per question: {avg_time:.2f} seconds")
        print(f"  Questions processed: {len(questions)}")
        
        return True
        
    except Exception as e:
        print(f"✗ Performance test error: {e}")
        return False

def main():
    """Run all tests"""
    print("Web Search System Test Suite")
    print("=" * 50)
    
    tests = [
        ("Environment", test_environment),
        ("Imports", test_imports),
        ("Google Search API", test_google_search_api),
        ("Content Extraction", test_content_extraction),
        ("OpenAI Integration", test_openai_integration),
        ("Basic Search", test_basic_search),
        ("Advanced Features", test_advanced_features),
        ("Performance", run_performance_test)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"✗ {test_name} test crashed: {e}")
            failed += 1
        
        time.sleep(0.5)  # Brief pause between tests
    
    print(f"\n{'=' * 50}")
    print(f"TEST RESULTS: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! The system is ready to use.")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        
    return failed == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
