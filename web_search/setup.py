#!/usr/bin/env python3
"""
Setup script for the Web Search Agent system
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def print_header(title):
    """Print a formatted header"""
    print(f"\n{'=' * 60}")
    print(f" {title}")
    print(f"{'=' * 60}")

def check_python_version():
    """Check if Python version is compatible"""
    print_header("CHECKING PYTHON VERSION")
    
    version = sys.version_info
    print(f"Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8 or higher is required")
        return False
    
    print("✅ Python version is compatible")
    return True

def install_dependencies():
    """Install required dependencies"""
    print_header("INSTALLING DEPENDENCIES")
    
    requirements_file = Path(__file__).parent / "requirements.txt"
    
    if not requirements_file.exists():
        print("❌ requirements.txt not found")
        return False
    
    try:
        # Install core dependencies only
        core_deps = [
            "requests>=2.31.0",
            "python-dotenv>=1.0.0", 
            "beautifulsoup4>=4.12.0",
            "openai>=1.0.0",
            "brotli>=1.0.9",
            "lxml>=4.9.0"
        ]
        
        print("Installing core dependencies...")
        for dep in core_deps:
            print(f"  Installing {dep.split('>=')[0]}...")
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", dep
            ], capture_output=True, text=True)
            
            if result.returncode != 0:
                print(f"❌ Failed to install {dep}")
                print(f"Error: {result.stderr}")
                return False
        
        print("✅ Core dependencies installed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error installing dependencies: {e}")
        return False

def setup_environment_file():
    """Create a .env file template"""
    print_header("SETTING UP ENVIRONMENT FILE")
    
    env_file = Path(__file__).parent / ".env"
    
    if env_file.exists():
        print("📄 .env file already exists")
        return True
    
    env_template = """# Web Search Agent Configuration
# Copy this file to .env and fill in your API keys

# Google Custom Search API
# Get your API key from: https://developers.google.com/custom-search/v1/introduction
GOOGLE_API_KEY=your_google_api_key_here

# Google Custom Search Engine ID  
# Create a custom search engine at: https://cse.google.com/cse/
GOOGLE_CSE_ID=your_custom_search_engine_id_here

# OpenAI API Key
# Get your API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here

# Optional Configuration
MAX_RESULTS_PER_QUERY=10
MAX_CONTENT_LENGTH=3000
AI_MODEL_NAME=gpt-4
AI_TEMPERATURE=0.3
LOG_LEVEL=INFO
"""
    
    try:
        with open(env_file, 'w') as f:
            f.write(env_template)
        
        print(f"✅ Created .env template at {env_file}")
        print("📝 Please edit the .env file and add your API keys")
        return True
        
    except Exception as e:
        print(f"❌ Error creating .env file: {e}")
        return False

def verify_installation():
    """Verify that the installation works"""
    print_header("VERIFYING INSTALLATION")
    
    try:
        # Test imports
        print("Testing imports...")
        import requests
        import bs4
        import openai
        import brotli
        print("✅ All required modules can be imported")
        
        # Test basic functionality
        print("Testing basic functionality...")
        from browser_tool import WebSearchAgent
        from config import WebSearchSystemConfig
        print("✅ Core modules loaded successfully")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Verification error: {e}")
        return False

def show_next_steps():
    """Show next steps to the user"""
    print_header("NEXT STEPS")
    
    print("🎉 Setup completed successfully!")
    print()
    print("To get started:")
    print()
    print("1. Edit the .env file and add your API keys:")
    print("   - Google API Key (Custom Search API)")
    print("   - Google Custom Search Engine ID")
    print("   - OpenAI API Key")
    print()
    print("2. Test the installation:")
    print("   python test_search.py")
    print()
    print("3. Try the example usage:")
    print("   python example_usage.py")
    print()
    print("4. Read the documentation:")
    print("   cat README.md")
    print()
    print("API Key Setup Instructions:")
    print("- Google Custom Search: https://developers.google.com/custom-search/v1/introduction")
    print("- Google CSE Setup: https://cse.google.com/cse/")
    print("- OpenAI API: https://platform.openai.com/api-keys")

def show_api_setup_guide():
    """Show detailed API setup guide"""
    print_header("API SETUP GUIDE")
    
    print("📋 GOOGLE CUSTOM SEARCH API SETUP:")
    print("1. Go to https://console.cloud.google.com/")
    print("2. Create a new project or select existing one")
    print("3. Enable the Custom Search API")
    print("4. Create credentials (API Key)")
    print("5. Copy the API key to your .env file")
    print()
    
    print("📋 GOOGLE CUSTOM SEARCH ENGINE SETUP:")
    print("1. Go to https://cse.google.com/cse/")
    print("2. Click 'Add' to create a new search engine")
    print("3. Enter '*' as the site to search (for web-wide search)")
    print("4. Create the search engine")
    print("5. Copy the Search Engine ID to your .env file")
    print()
    
    print("📋 OPENAI API SETUP:")
    print("1. Go to https://platform.openai.com/")
    print("2. Sign up or log in to your account")
    print("3. Go to API Keys section")
    print("4. Create a new API key")
    print("5. Copy the API key to your .env file")
    print("6. Make sure you have credits in your account")

def main():
    """Main setup function"""
    print("Web Search Agent Setup")
    print("This script will help you set up the Web Search Agent system")
    
    # Check Python version
    if not check_python_version():
        return False
    
    # Install dependencies
    if not install_dependencies():
        return False
    
    # Setup environment file
    if not setup_environment_file():
        return False
    
    # Verify installation
    if not verify_installation():
        return False
    
    # Show next steps
    show_next_steps()
    
    # Ask if user wants to see API setup guide
    if input("\nWould you like to see the detailed API setup guide? (y/n): ").lower().startswith('y'):
        show_api_setup_guide()
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎉 Setup completed successfully!")
        else:
            print("\n❌ Setup failed. Please check the errors above.")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️  Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error during setup: {e}")
        sys.exit(1)
