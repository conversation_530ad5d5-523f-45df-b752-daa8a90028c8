"""
Configuration settings for the web search system
"""

import os
from dataclasses import dataclass
from typing import Dict, List, Optional

@dataclass
class APIConfig:
    """API configuration settings"""
    google_api_key: str = ""
    google_cse_id: str = ""
    openai_api_key: str = ""
    
    def __post_init__(self):
        # Load from environment variables if not provided
        if not self.google_api_key:
            self.google_api_key = os.getenv("GOOGLE_API_KEY", "")
        if not self.google_cse_id:
            self.google_cse_id = os.getenv("GOOGLE_CSE_ID", "")
        if not self.openai_api_key:
            self.openai_api_key = os.getenv("OPENAI_API_KEY", "")
    
    def validate(self) -> List[str]:
        """Validate configuration and return list of errors"""
        errors = []
        if not self.google_api_key:
            errors.append("Google API key is required")
        if not self.google_cse_id:
            errors.append("Google Custom Search Engine ID is required")
        if not self.openai_api_key:
            errors.append("OpenAI API key is required")
        return errors

@dataclass
class SearchConfig:
    """Search behavior configuration"""
    max_results_per_query: int = 10
    max_total_results: int = 20
    max_content_length: int = 3000
    timeout_seconds: int = 20
    retry_attempts: int = 3
    retry_delay_seconds: float = 1.0
    rate_limit_delay: float = 0.5
    
    # Content filtering
    skip_file_extensions: List[str] = None
    allowed_domains: List[str] = None
    blocked_domains: List[str] = None
    
    def __post_init__(self):
        if self.skip_file_extensions is None:
            self.skip_file_extensions = [
                '.pdf', '.doc', '.docx', '.ppt', '.pptx', '.xls', '.xlsx',
                '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg',
                '.mp4', '.avi', '.mov', '.wmv', '.mp3', '.wav',
                '.zip', '.rar', '.tar', '.gz'
            ]
        
        if self.blocked_domains is None:
            self.blocked_domains = [
                'pinterest.com',
                'instagram.com',
                'facebook.com',
                'twitter.com',
                'tiktok.com'
            ]

@dataclass
class AIConfig:
    """AI model configuration"""
    model_name: str = "gpt-4"
    max_tokens: int = 1500
    temperature: float = 0.3
    system_prompt: str = ""
    
    def __post_init__(self):
        if not self.system_prompt:
            self.system_prompt = """You are an expert research assistant. Your task is to provide accurate, comprehensive, and well-structured answers based on the provided web search results.

Guidelines:
1. Synthesize information from multiple sources to provide a complete answer
2. Be factual and cite specific information when possible
3. If sources contradict each other, mention the discrepancy
4. Structure your answer clearly with proper formatting
5. If the information is insufficient, acknowledge the limitations
6. Focus on the most recent and reliable information
7. Provide specific details, numbers, dates, and examples when available
8. Use bullet points or numbered lists for clarity when appropriate
9. Maintain a professional and informative tone"""

@dataclass
class LoggingConfig:
    """Logging configuration"""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    log_to_file: bool = False
    log_file_path: str = "web_search.log"

class WebSearchSystemConfig:
    """Main configuration class for the web search system"""
    
    def __init__(self):
        self.api = APIConfig()
        self.search = SearchConfig()
        self.ai = AIConfig()
        self.logging = LoggingConfig()
    
    def validate(self) -> List[str]:
        """Validate all configuration settings"""
        errors = []
        errors.extend(self.api.validate())
        
        # Validate search config
        if self.search.max_results_per_query < 1:
            errors.append("max_results_per_query must be at least 1")
        if self.search.max_results_per_query > 10:
            errors.append("max_results_per_query cannot exceed 10 (Google API limit)")
        if self.search.timeout_seconds < 5:
            errors.append("timeout_seconds should be at least 5")
        
        # Validate AI config
        if self.ai.max_tokens < 100:
            errors.append("max_tokens should be at least 100")
        if not 0 <= self.ai.temperature <= 2:
            errors.append("temperature must be between 0 and 2")
        
        return errors
    
    def load_from_env(self):
        """Load configuration from environment variables"""
        # API settings
        self.api.google_api_key = os.getenv("GOOGLE_API_KEY", self.api.google_api_key)
        self.api.google_cse_id = os.getenv("GOOGLE_CSE_ID", self.api.google_cse_id)
        self.api.openai_api_key = os.getenv("OPENAI_API_KEY", self.api.openai_api_key)
        
        # Search settings
        self.search.max_results_per_query = int(os.getenv("MAX_RESULTS_PER_QUERY", self.search.max_results_per_query))
        self.search.max_content_length = int(os.getenv("MAX_CONTENT_LENGTH", self.search.max_content_length))
        self.search.timeout_seconds = int(os.getenv("TIMEOUT_SECONDS", self.search.timeout_seconds))
        
        # AI settings
        self.ai.model_name = os.getenv("AI_MODEL_NAME", self.ai.model_name)
        self.ai.max_tokens = int(os.getenv("AI_MAX_TOKENS", self.ai.max_tokens))
        self.ai.temperature = float(os.getenv("AI_TEMPERATURE", self.ai.temperature))
        
        # Logging settings
        self.logging.level = os.getenv("LOG_LEVEL", self.logging.level)
        self.logging.log_to_file = os.getenv("LOG_TO_FILE", "false").lower() == "true"
    
    def to_dict(self) -> Dict:
        """Convert configuration to dictionary"""
        return {
            "api": {
                "google_api_key": "***" if self.api.google_api_key else "",
                "google_cse_id": self.api.google_cse_id,
                "openai_api_key": "***" if self.api.openai_api_key else ""
            },
            "search": {
                "max_results_per_query": self.search.max_results_per_query,
                "max_total_results": self.search.max_total_results,
                "max_content_length": self.search.max_content_length,
                "timeout_seconds": self.search.timeout_seconds,
                "retry_attempts": self.search.retry_attempts,
                "skip_file_extensions": self.search.skip_file_extensions,
                "blocked_domains": self.search.blocked_domains
            },
            "ai": {
                "model_name": self.ai.model_name,
                "max_tokens": self.ai.max_tokens,
                "temperature": self.ai.temperature
            },
            "logging": {
                "level": self.logging.level,
                "log_to_file": self.logging.log_to_file
            }
        }

# Default configuration instance
default_config = WebSearchSystemConfig()

# Search depth configurations
SEARCH_DEPTH_CONFIGS = {
    "quick": {
        "max_queries": 1,
        "max_results_per_query": 5,
        "max_content_extraction": 3
    },
    "standard": {
        "max_queries": 3,
        "max_results_per_query": 8,
        "max_content_extraction": 5
    },
    "deep": {
        "max_queries": 6,
        "max_results_per_query": 10,
        "max_content_extraction": 8
    }
}

# Domain authority scores for ranking
DOMAIN_AUTHORITY_SCORES = {
    'wikipedia.org': 0.95,
    'britannica.com': 0.90,
    'reuters.com': 0.85,
    'bbc.com': 0.85,
    'cnn.com': 0.80,
    'nytimes.com': 0.85,
    'washingtonpost.com': 0.85,
    'theguardian.com': 0.80,
    'nature.com': 0.95,
    'science.org': 0.95,
    'ieee.org': 0.90,
    'arxiv.org': 0.85,
    'pubmed.ncbi.nlm.nih.gov': 0.90,
    'scholar.google.com': 0.85,
    'stackoverflow.com': 0.80,
    'github.com': 0.75,
    'medium.com': 0.60,
    'reddit.com': 0.50,
    'quora.com': 0.55,
    'youtube.com': 0.60
}

# File extensions to skip during content extraction
SKIP_EXTENSIONS = [
    '.pdf', '.doc', '.docx', '.ppt', '.pptx', '.xls', '.xlsx',
    '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg', '.webp',
    '.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv',
    '.mp3', '.wav', '.flac', '.aac', '.ogg',
    '.zip', '.rar', '.tar', '.gz', '.7z',
    '.exe', '.dmg', '.pkg', '.deb', '.rpm'
]

def setup_logging(config: LoggingConfig):
    """Setup logging based on configuration"""
    import logging
    
    level = getattr(logging, config.level.upper(), logging.INFO)
    
    if config.log_to_file:
        logging.basicConfig(
            level=level,
            format=config.format,
            handlers=[
                logging.FileHandler(config.log_file_path),
                logging.StreamHandler()
            ]
        )
    else:
        logging.basicConfig(
            level=level,
            format=config.format
        )
