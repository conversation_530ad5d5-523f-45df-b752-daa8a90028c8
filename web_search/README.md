# Web Search Agent with AI-Powered Answers

A comprehensive web search system that uses Google Custom Search API to find relevant information and OpenAI's GPT models to generate accurate, well-structured answers. The system is designed to handle deep, complex questions by searching multiple sources and synthesizing information intelligently.

## Features

### Core Functionality
- **Multi-depth Search**: Quick, standard, and deep search modes
- **AI-Powered Answers**: Uses GPT-4 to synthesize information from multiple sources
- **Content Extraction**: Intelligent extraction and cleaning of web page content
- **Source Ranking**: Advanced ranking based on authority, relevance, and freshness
- **Query Optimization**: Automatic optimization of search queries for better results

### Advanced Features
- **Batch Processing**: Handle multiple questions efficiently
- **Content Analysis**: Extract key facts, dates, numbers, and entities
- **Quality Scoring**: Evaluate content quality and reliability
- **Caching Support**: Reduce API calls with intelligent caching
- **Rate Limiting**: Respect API limits and avoid being blocked

## Installation

1. **Clone or download the files**
2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables**:
   ```bash
   export GOOGLE_API_KEY="your_google_api_key"
   export GOOGLE_CSE_ID="your_custom_search_engine_id"
   export OPENAI_API_KEY="your_openai_api_key"
   ```

## Quick Start

### Basic Usage

```python
from browser_tool import perform_web_search_with_ai

# Simple search and answer
question = "What are the latest developments in artificial intelligence?"
result = perform_web_search_with_ai(question, "standard")

print(f"Answer: {result['answer']}")
print(f"Sources: {len(result['sources'])}")
```

### Advanced Usage

```python
from browser_tool import WebSearchAgent
from config import WebSearchSystemConfig

# Create custom configuration
config = WebSearchSystemConfig()
config.search.max_results_per_query = 8
config.ai.temperature = 0.2

# Create agent with custom config
agent = WebSearchAgent(config.search)

# Perform deep search
result = agent.search_and_answer(
    "How does quantum computing impact cybersecurity?", 
    "deep"
)
```

## API Reference

### Main Functions

#### `perform_web_search_with_ai(question, search_depth="standard")`
Perform web search and generate AI-powered answer.

**Parameters:**
- `question` (str): The question to search for and answer
- `search_depth` (str): "quick", "standard", or "deep"

**Returns:**
- Dictionary with answer, sources, and metadata

#### `search_multiple_questions(questions, search_depth="standard")`
Process multiple questions in batch.

**Parameters:**
- `questions` (List[str]): List of questions to answer
- `search_depth` (str): Search depth for all questions

**Returns:**
- List of answer dictionaries

### Classes

#### `WebSearchAgent`
Main agent class that orchestrates search and answer generation.

**Methods:**
- `search_and_answer(question, search_depth)`: Main search and answer method
- `_generate_search_queries(question, depth)`: Generate multiple search queries
- `_perform_search(query)`: Execute single search query
- `_extract_content_from_results(results)`: Extract content from web pages
- `_generate_answer(question, results)`: Generate AI-powered answer

#### `GoogleSearchAPI`
Google Custom Search API wrapper.

**Methods:**
- `search(query, num_results, **kwargs)`: Perform Google search
- Handles API errors and rate limiting

#### `WebContentExtractor`
Extract and clean content from web pages.

**Methods:**
- `extract_content(url)`: Extract clean text from URL
- `_clean_html_content(html, url)`: Clean and parse HTML
- `_find_main_content(soup)`: Find main content in HTML

## Configuration

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `GOOGLE_API_KEY` | Google Custom Search API key | Yes |
| `GOOGLE_CSE_ID` | Google Custom Search Engine ID | Yes |
| `OPENAI_API_KEY` | OpenAI API key | Yes |
| `MAX_RESULTS_PER_QUERY` | Maximum results per search query | No |
| `MAX_CONTENT_LENGTH` | Maximum content length to extract | No |
| `AI_MODEL_NAME` | OpenAI model to use | No |

### Search Depths

- **Quick**: 1 query, 5 results, 3 content extractions
- **Standard**: 3 queries, 8 results, 5 content extractions  
- **Deep**: 6 queries, 10 results, 8 content extractions

## Examples

### Example 1: Basic Question Answering
```python
result = perform_web_search_with_ai(
    "What is the current population of Japan?", 
    "quick"
)
print(result['answer'])
```

### Example 2: Complex Technical Question
```python
result = perform_web_search_with_ai(
    "How do transformer neural networks work and what are their applications?", 
    "deep"
)
print(result['answer'])
for source in result['sources']:
    print(f"- {source['title']}: {source['url']}")
```

### Example 3: Batch Processing
```python
questions = [
    "What is climate change?",
    "How do electric cars work?",
    "What are the benefits of renewable energy?"
]

results = search_multiple_questions(questions, "standard")
for result in results:
    print(f"Q: {result['question']}")
    print(f"A: {result['answer'][:200]}...")
```

## Advanced Features

### Content Analysis
```python
from advanced_search_utils import ContentAnalyzer

analyzer = ContentAnalyzer()
facts = analyzer.extract_key_facts(content)
summary = analyzer.get_content_summary(content)
quality = analyzer.calculate_content_quality(content)
```

### Query Optimization
```python
from advanced_search_utils import SearchQueryOptimizer

optimizer = SearchQueryOptimizer()
optimized_query = optimizer.optimize_query("artificial intelligence")
related_queries = optimizer.generate_related_queries("AI")
```

### Custom Ranking
```python
from advanced_search_utils import SearchResultRanker

ranker = SearchResultRanker()
ranked_results = ranker.rank_results(results, query)
```

## Error Handling

The system includes comprehensive error handling:

- **API Errors**: Graceful handling of Google API and OpenAI API errors
- **Network Issues**: Retry logic with exponential backoff
- **Content Extraction**: Fallback methods for difficult websites
- **Rate Limiting**: Automatic rate limiting to avoid API quotas

## Performance Optimization

- **Concurrent Processing**: Parallel content extraction
- **Caching**: Cache search results to reduce API calls
- **Content Filtering**: Skip non-relevant file types
- **Smart Truncation**: Intelligent text truncation at sentence boundaries

## Limitations

- **API Quotas**: Limited by Google Custom Search and OpenAI API quotas
- **Real-time Data**: May not have access to very recent information
- **Paywalled Content**: Cannot access content behind paywalls
- **Dynamic Content**: Limited access to JavaScript-rendered content

## Troubleshooting

### Common Issues

1. **"Google API key is required"**
   - Set the `GOOGLE_API_KEY` environment variable
   - Ensure the API key has Custom Search API enabled

2. **"No results found"**
   - Check if the Custom Search Engine is configured correctly
   - Verify the `GOOGLE_CSE_ID` is correct

3. **"OpenAI API error"**
   - Verify the `OPENAI_API_KEY` is valid
   - Check if you have sufficient API credits

### Debug Mode
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review the example usage
3. Create an issue with detailed information about your problem
