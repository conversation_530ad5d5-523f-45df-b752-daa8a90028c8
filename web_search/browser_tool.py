import os
import requests
import json
import time
import logging
from typing import List, Dict, Any, Optional, Tuple
from bs4 import BeautifulSoup
from openai import OpenAI
from datetime import datetime
import urllib.parse
import urllib.request
import urllib.error
import gzip
import brotli
import zlib
import re
from dataclasses import dataclass

# === Configuration ===
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

client = OpenAI(api_key=os.getenv("OPENAI_API_KEY", "********************************************************************************************************************************************************************"))
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY", "AIzaSyARbP4IB5Ug_7I7VpEif_scti1zNf9qqmI")
GOOGLE_CSE_ID = os.getenv("GOOGLE_CSE_ID", "a467db58f3ab4445c")

GOOGLE_SEARCH_URL = "https://www.googleapis.com/customsearch/v1"

@dataclass
class SearchResult:
    """Data class for search results"""
    title: str
    url: str
    snippet: str
    content: str = ""
    relevance_score: float = 0.0

@dataclass
class WebSearchConfig:
    """Configuration for web search"""
    max_results: int = 10
    max_content_length: int = 3000
    timeout: int = 20
    retry_attempts: int = 3
    retry_delay: float = 1.0

class GoogleSearchAPI:
    """Google Custom Search API wrapper"""

    def __init__(self, api_key: str = None, cse_id: str = None):
        self.api_key = api_key or GOOGLE_API_KEY
        self.cse_id = cse_id or GOOGLE_CSE_ID
        self.base_url = GOOGLE_SEARCH_URL

        if not self.api_key:
            raise ValueError("Google API key is required")
        if not self.cse_id:
            raise ValueError("Google Custom Search Engine ID is required")

    def search(self, query: str, num_results: int = 10, **kwargs) -> List[Dict[str, Any]]:
        """
        Perform Google search and return results

        Args:
            query: Search query string
            num_results: Number of results to return (max 10 per request)
            **kwargs: Additional search parameters

        Returns:
            List of search result dictionaries
        """
        try:
            params = {
                'q': query,
                'key': self.api_key,
                'cx': self.cse_id,
                'num': min(num_results, 10),  # Google API limit
                **kwargs
            }

            # Remove empty parameters
            params = {k: v for k, v in params.items() if v is not None and v != ''}

            logger.info(f"Searching for: {query}")
            response = requests.get(self.base_url, params=params, timeout=30)
            response.raise_for_status()

            data = response.json()

            if 'error' in data:
                error_msg = data['error'].get('message', 'Unknown Google API error')
                logger.error(f"Google API error: {error_msg}")
                raise Exception(f"Google API error: {error_msg}")

            return data.get('items', [])

        except requests.exceptions.RequestException as e:
            logger.error(f"Request error during search: {e}")
            raise Exception(f"Search request failed: {e}")
        except Exception as e:
            logger.error(f"Unexpected error during search: {e}")
            raise

class WebContentExtractor:
    """Extract and clean content from web pages"""

    def __init__(self, config: WebSearchConfig = None):
        self.config = config or WebSearchConfig()

    def extract_content(self, url: str) -> str:
        """
        Extract clean text content from a web page

        Args:
            url: URL to extract content from

        Returns:
            Cleaned text content
        """
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            }

            request = urllib.request.Request(url, headers=headers)

            with urllib.request.urlopen(request, timeout=self.config.timeout) as response:
                content_encoding = response.headers.get('Content-Encoding', '').lower()
                raw_data = response.read()

                # Handle different compression formats
                if content_encoding == 'gzip':
                    content = gzip.decompress(raw_data)
                elif content_encoding == 'deflate':
                    content = zlib.decompress(raw_data)
                elif content_encoding == 'br':
                    content = brotli.decompress(raw_data)
                else:
                    content = raw_data

                # Additional gzip check
                if content[:2] == b'\x1f\x8b':
                    content = gzip.decompress(content)

            # Decode content with multiple encoding attempts
            html_content = self._decode_content(content)

            if not html_content or len(html_content.strip()) < 100:
                return f"Retrieved content is too short or empty from {url}"

            # Parse and clean HTML
            return self._clean_html_content(html_content, url)

        except urllib.error.HTTPError as e:
            if e.code == 403:
                return f"Access denied (403) for {url}. The website blocks automated requests."
            else:
                return f"HTTP Error {e.code}: {e.reason} for {url}"
        except urllib.error.URLError as e:
            return f"URL Error for {url}: {e.reason}"
        except Exception as e:
            return f"Error fetching content from {url}: {str(e)}"

    def _decode_content(self, content: bytes) -> str:
        """Decode bytes content to string with multiple encoding attempts"""
        html_content = ""
        for encoding in ['utf-8', 'utf-8-sig', 'latin1', 'cp1252', 'iso-8859-1']:
            try:
                html_content = content.decode(encoding)
                break
            except Exception:
                continue

        if not html_content:
            html_content = content.decode('utf-8', errors='replace')

        return html_content

    def _clean_html_content(self, html_content: str, url: str) -> str:
        """Clean and extract meaningful content from HTML"""
        soup = BeautifulSoup(html_content, 'html.parser')

        # Remove unwanted elements
        for element in soup(["script", "style", "nav", "footer", "header", "aside", "iframe", "noscript"]):
            element.decompose()

        # Find main content using various selectors
        main_content = self._find_main_content(soup)

        if not main_content:
            main_content = soup.find('body') or soup

        # Extract and clean text
        text = main_content.get_text(separator=' ', strip=True)
        text = ' '.join(text.split())  # Normalize whitespace

        if len(text) < 50:
            return f"Extracted content is too short from {url}"

        # Truncate if too long
        if len(text) > self.config.max_content_length:
            text = self._truncate_text(text, self.config.max_content_length)

        return text

    def _find_main_content(self, soup: BeautifulSoup) -> Optional[Any]:
        """Find the main content element in the HTML"""
        selectors = [
            'main', 'article', '[role="main"]',
            '.main-content', '.content', '#content', '.post-content',
            '.entry-content', '.article-content', '.page-content',
            '.post', '.entry', '.article'
        ]

        for selector in selectors:
            elements = soup.select(selector)
            if elements:
                return max(elements, key=lambda x: len(x.get_text()))

        return None

    def _truncate_text(self, text: str, max_length: int) -> str:
        """Truncate text at sentence boundary if possible"""
        if len(text) <= max_length:
            return text

        truncated = text[:max_length]
        last_sentence_end = max(
            truncated.rfind('.'),
            truncated.rfind('!'),
            truncated.rfind('?')
        )

        if last_sentence_end > max_length * 0.8:
            return truncated[:last_sentence_end + 1]
        else:
            return truncated + "..."

class WebSearchAgent:
    """Main agent that performs web search and generates answers using AI model"""

    def __init__(self, config: WebSearchConfig = None, openai_client: OpenAI = None):
        self.config = config or WebSearchConfig()
        self.search_api = GoogleSearchAPI()
        self.content_extractor = WebContentExtractor(self.config)
        self.openai_client = openai_client or client

    def search_and_answer(self, question: str, search_depth: str = "standard") -> Dict[str, Any]:
        """
        Perform comprehensive web search and generate an accurate answer

        Args:
            question: The question to answer
            search_depth: "quick", "standard", or "deep" search

        Returns:
            Dictionary containing the answer, sources, and metadata
        """
        try:
            logger.info(f"Processing question: {question}")

            # Determine search strategy based on depth
            search_queries = self._generate_search_queries(question, search_depth)

            # Perform searches and collect results
            all_results = []
            for query in search_queries:
                results = self._perform_search(query)
                all_results.extend(results)

            # Remove duplicates and rank results
            unique_results = self._deduplicate_results(all_results)
            ranked_results = self._rank_results(unique_results, question)

            # Extract content from top results
            enriched_results = self._extract_content_from_results(ranked_results[:self.config.max_results])

            # Generate comprehensive answer
            answer = self._generate_answer(question, enriched_results)

            return {
                "question": question,
                "answer": answer,
                "sources": [
                    {
                        "title": result.title,
                        "url": result.url,
                        "relevance_score": result.relevance_score
                    }
                    for result in enriched_results[:5]  # Top 5 sources
                ],
                "search_queries_used": search_queries,
                "total_sources_found": len(unique_results),
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error in search_and_answer: {e}")
            return {
                "question": question,
                "answer": f"I apologize, but I encountered an error while searching for information: {str(e)}",
                "sources": [],
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    def _generate_search_queries(self, question: str, depth: str) -> List[str]:
        """Generate multiple search queries based on the question and search depth"""
        base_query = question.strip()
        queries = [base_query]

        if depth == "quick":
            return queries[:1]
        elif depth == "standard":
            # Add 1-2 variations
            queries.extend([
                f"{base_query} latest information",
                f"{base_query} 2024 2025"
            ])
            return queries[:3]
        elif depth == "deep":
            # Add multiple variations and related queries
            queries.extend([
                f"{base_query} latest information",
                f"{base_query} 2024 2025",
                f"{base_query} detailed explanation",
                f"{base_query} expert analysis",
                f"{base_query} comprehensive guide"
            ])
            return queries[:6]

        return queries

    def _perform_search(self, query: str) -> List[SearchResult]:
        """Perform a single search and return SearchResult objects"""
        try:
            raw_results = self.search_api.search(query, num_results=self.config.max_results)

            search_results = []
            for item in raw_results:
                url = item.get('link', '')

                # Skip non-HTML files
                if self._is_non_html_file(url):
                    continue

                result = SearchResult(
                    title=self._clean_html_tags(item.get('title', '')),
                    url=url,
                    snippet=self._clean_html_tags(item.get('snippet', ''))
                )
                search_results.append(result)

            return search_results

        except Exception as e:
            logger.error(f"Error performing search for query '{query}': {e}")
            return []

    def _is_non_html_file(self, url: str) -> bool:
        """Check if URL points to a non-HTML file"""
        non_html_extensions = {'.pdf', '.jpg', '.jpeg', '.png', '.gif', '.doc', '.docx', '.ppt', '.pptx', '.xls', '.xlsx'}
        return any(url.lower().endswith(ext) for ext in non_html_extensions)

    def _deduplicate_results(self, results: List[SearchResult]) -> List[SearchResult]:
        """Remove duplicate results based on URL"""
        seen_urls = set()
        unique_results = []

        for result in results:
            if result.url not in seen_urls:
                seen_urls.add(result.url)
                unique_results.append(result)

        return unique_results

    def _rank_results(self, results: List[SearchResult], question: str) -> List[SearchResult]:
        """Rank results based on relevance to the question"""
        question_words = set(question.lower().split())

        for result in results:
            # Simple relevance scoring based on keyword matches
            title_words = set(result.title.lower().split())
            snippet_words = set(result.snippet.lower().split())

            title_matches = len(question_words.intersection(title_words))
            snippet_matches = len(question_words.intersection(snippet_words))

            # Weight title matches more heavily
            result.relevance_score = (title_matches * 2 + snippet_matches) / len(question_words)

        # Sort by relevance score (descending)
        return sorted(results, key=lambda x: x.relevance_score, reverse=True)

    def _extract_content_from_results(self, results: List[SearchResult]) -> List[SearchResult]:
        """Extract full content from search results"""
        enriched_results = []

        for result in results:
            try:
                content = self.content_extractor.extract_content(result.url)

                # Skip results with extraction errors or insufficient content
                if (content.startswith("Access denied") or
                    content.startswith("Error fetching") or
                    "too short" in content or
                    "empty" in content):
                    continue

                result.content = content
                enriched_results.append(result)

            except Exception as e:
                logger.warning(f"Failed to extract content from {result.url}: {e}")
                continue

        return enriched_results

    def _generate_answer(self, question: str, results: List[SearchResult]) -> str:
        """Generate a comprehensive answer using the AI model"""
        if not results:
            return "I couldn't find sufficient reliable information to answer your question. Please try rephrasing your question or being more specific."

        # Prepare context from search results
        context_parts = []
        for i, result in enumerate(results[:5], 1):  # Use top 5 results
            context_parts.append(f"Source {i} ({result.title}):\n{result.content[:1500]}...")  # Limit content length

        context = "\n\n".join(context_parts)

        # Create the prompt for the AI model
        system_prompt = """You are an expert research assistant. Your task is to provide accurate, comprehensive, and well-structured answers based on the provided web search results.

Guidelines:
1. Synthesize information from multiple sources to provide a complete answer
2. Be factual and cite specific information when possible
3. If sources contradict each other, mention the discrepancy
4. Structure your answer clearly with proper formatting
5. If the information is insufficient, acknowledge the limitations
6. Focus on the most recent and reliable information
7. Provide specific details, numbers, dates, and examples when available"""

        user_prompt = f"""Question: {question}

Based on the following search results, please provide a comprehensive and accurate answer:

{context}

Please provide a detailed answer that synthesizes the information from these sources."""

        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                max_tokens=1500,
                temperature=0.3  # Lower temperature for more factual responses
            )

            return response.choices[0].message.content.strip()

        except Exception as e:
            logger.error(f"Error generating answer with AI model: {e}")
            # Fallback: provide a basic summary
            return self._generate_fallback_answer(question, results)

    def _generate_fallback_answer(self, question: str, results: List[SearchResult]) -> str:
        """Generate a basic answer without AI model"""
        if not results:
            return "No reliable information found."

        # Simple extraction of key information
        answer_parts = [f"Based on my search, here's what I found about '{question}':\n"]

        for i, result in enumerate(results[:3], 1):
            snippet = result.snippet[:200] + "..." if len(result.snippet) > 200 else result.snippet
            answer_parts.append(f"{i}. {snippet} (Source: {result.title})")

        return "\n\n".join(answer_parts)

    def _clean_html_tags(self, text: str) -> str:
        """Clean HTML tags and entities from text"""
        if not text:
            return ""

        # HTML entities mapping
        html_entities = {
            '&nbsp;': ' ', '&amp;': '&', '&lt;': '<', '&gt;': '>',
            '&quot;': '"', '&#39;': "'", '&apos;': "'", '&cent;': '¢',
            '&pound;': '£', '&yen;': '¥', '&euro;': '€', '&copy;': '©',
            '&reg;': '®', '&trade;': '™'
        }

        # Replace HTML entities
        for entity, replacement in html_entities.items():
            text = text.replace(entity, replacement)

        # Remove HTML tags
        text = re.sub('<[^<]+?>', '', text)

        # Normalize whitespace
        text = re.sub(r'\s+', ' ', text)

        return text.strip()

# === Main Functions ===

def perform_web_search_with_ai(question: str, search_depth: str = "standard") -> Dict[str, Any]:
    """
    Main function to perform web search and generate AI-powered answers

    Args:
        question: The question to search for and answer
        search_depth: "quick", "standard", or "deep" search

    Returns:
        Dictionary containing answer, sources, and metadata
    """
    agent = WebSearchAgent()
    return agent.search_and_answer(question, search_depth)

def search_multiple_questions(questions: List[str], search_depth: str = "standard") -> List[Dict[str, Any]]:
    """
    Search and answer multiple questions

    Args:
        questions: List of questions to answer
        search_depth: Search depth for all questions

    Returns:
        List of answer dictionaries
    """
    agent = WebSearchAgent()
    results = []

    for question in questions:
        try:
            result = agent.search_and_answer(question, search_depth)
            results.append(result)
            time.sleep(1)  # Rate limiting
        except Exception as e:
            logger.error(f"Error processing question '{question}': {e}")
            results.append({
                "question": question,
                "answer": f"Error processing question: {str(e)}",
                "sources": [],
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })

    return results

def get_search_suggestions(topic: str) -> List[str]:
    """
    Generate search suggestions for a given topic

    Args:
        topic: The topic to generate suggestions for

    Returns:
        List of suggested search queries
    """
    suggestions = [
        f"What is {topic}?",
        f"How does {topic} work?",
        f"{topic} latest news 2024",
        f"{topic} benefits and drawbacks",
        f"{topic} expert opinion",
        f"{topic} future trends",
        f"{topic} comparison alternatives"
    ]

    return suggestions

# === Example Usage and Testing ===

def main():
    """Example usage of the web search system"""

    # Example questions to test
    test_questions = [
        "Who is the current president of India?",
        "What are the latest developments in artificial intelligence in 2024?",
        "How does quantum computing work?",
        "What are the environmental impacts of electric vehicles?"
    ]

    print("=== Web Search Agent with AI-Powered Answers ===\n")

    # Test single question with different search depths
    question = "What are the latest developments in artificial intelligence in 2024?"

    print(f"Question: {question}\n")

    for depth in ["quick", "standard", "deep"]:
        print(f"--- {depth.upper()} SEARCH ---")
        result = perform_web_search_with_ai(question, depth)

        print(f"Answer: {result['answer'][:300]}...")
        print(f"Sources found: {result['total_sources_found']}")
        print(f"Top sources:")
        for i, source in enumerate(result['sources'][:3], 1):
            print(f"  {i}. {source['title']} (Score: {source['relevance_score']:.2f})")
        print()

    # Test multiple questions
    print("--- MULTIPLE QUESTIONS TEST ---")
    results = search_multiple_questions(test_questions[:2], "standard")

    for result in results:
        print(f"Q: {result['question']}")
        print(f"A: {result['answer'][:200]}...")
        print(f"Sources: {len(result['sources'])}")
        print()

if __name__ == "__main__":
    main()
