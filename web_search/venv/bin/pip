#!/bin/sh
'''exec' "/Users/<USER>/Library/CloudStorage/OneDrive-MeyiCloudSolutionsPrivateLimited/backup/customer project/New Project Opportunities/AI/bedrock-flow-poc/web_search/venv/bin/python3" "$0" "$@"
' '''
# -*- coding: utf-8 -*-
import re
import sys
from pip._internal.cli.main import main
if __name__ == '__main__':
    sys.argv[0] = re.sub(r'(-script\.pyw|\.exe)?$', '', sys.argv[0])
    sys.exit(main())
