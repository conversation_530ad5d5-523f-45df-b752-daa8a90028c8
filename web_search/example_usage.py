#!/usr/bin/env python3
"""
Example usage of the Web Search Agent with AI-powered answers
"""

import json
import time
from typing import List, Dict
from browser_tool import (
    WebSearchAgent, 
    perform_web_search_with_ai, 
    search_multiple_questions,
    get_search_suggestions
)
from advanced_search_utils import (
    SearchQueryOptimizer,
    ContentAnalyzer,
    SearchResultRanker,
    is_question_answerable
)
from config import WebSearchSystemConfig, setup_logging

def demo_basic_search():
    """Demonstrate basic search functionality"""
    print("=== BASIC SEARCH DEMO ===\n")
    
    questions = [
        "Who is the current president of India?",
        "What are the latest developments in artificial intelligence?",
        "How does quantum computing work?",
        "What are the environmental benefits of electric vehicles?"
    ]
    
    for question in questions:
        print(f"Question: {question}")
        
        # Check if question is answerable
        answerable, reason = is_question_answerable(question)
        if not answerable:
            print(f"Cannot answer: {reason}\n")
            continue
        
        try:
            result = perform_web_search_with_ai(question, "standard")
            
            print(f"Answer: {result['answer'][:300]}...")
            print(f"Sources found: {result['total_sources_found']}")
            print(f"Top source: {result['sources'][0]['title'] if result['sources'] else 'None'}")
            print("-" * 50)
            
        except Exception as e:
            print(f"Error: {e}")
        
        time.sleep(1)  # Rate limiting

def demo_search_depths():
    """Demonstrate different search depths"""
    print("\n=== SEARCH DEPTH COMPARISON ===\n")
    
    question = "What are the latest breakthroughs in renewable energy technology?"
    
    for depth in ["quick", "standard", "deep"]:
        print(f"--- {depth.upper()} SEARCH ---")
        
        start_time = time.time()
        result = perform_web_search_with_ai(question, depth)
        end_time = time.time()
        
        print(f"Time taken: {end_time - start_time:.2f} seconds")
        print(f"Sources found: {result['total_sources_found']}")
        print(f"Answer length: {len(result['answer'])} characters")
        print(f"Answer preview: {result['answer'][:200]}...")
        print()

def demo_query_optimization():
    """Demonstrate query optimization"""
    print("\n=== QUERY OPTIMIZATION DEMO ===\n")
    
    optimizer = SearchQueryOptimizer()
    
    test_queries = [
        "artificial intelligence machine learning",
        "how to cook pasta",
        "climate change effects on environment",
        "best programming languages for beginners"
    ]
    
    for query in test_queries:
        optimized = optimizer.optimize_query(query)
        related = optimizer.generate_related_queries(query)
        
        print(f"Original: {query}")
        print(f"Optimized: {optimized}")
        print(f"Related queries: {related[:3]}")
        print("-" * 40)

def demo_content_analysis():
    """Demonstrate content analysis features"""
    print("\n=== CONTENT ANALYSIS DEMO ===\n")
    
    # Sample content for analysis
    sample_content = """
    Artificial Intelligence (AI) has seen remarkable progress in 2024, with breakthrough developments 
    in large language models, computer vision, and robotics. According to recent studies, AI adoption 
    in enterprises has increased by 35% compared to 2023. Major tech companies have invested over 
    $50 billion in AI research this year. Key developments include GPT-4's successor, advanced 
    autonomous vehicles, and AI-powered drug discovery platforms that have reduced development 
    time by 40%. The global AI market is projected to reach $1.8 trillion by 2030.
    """
    
    analyzer = ContentAnalyzer()
    
    # Extract key facts
    facts = analyzer.extract_key_facts(sample_content)
    print("Key facts extracted:")
    for fact_type, values in facts.items():
        print(f"  {fact_type}: {values}")
    
    # Generate summary
    summary = analyzer.get_content_summary(sample_content, max_sentences=2)
    print(f"\nSummary: {summary}")
    
    # Calculate quality score
    quality = analyzer.calculate_content_quality(sample_content)
    print(f"Content quality score: {quality:.2f}")

def demo_advanced_search():
    """Demonstrate advanced search with custom configuration"""
    print("\n=== ADVANCED SEARCH DEMO ===\n")
    
    # Create custom configuration
    config = WebSearchSystemConfig()
    config.search.max_results_per_query = 8
    config.search.max_content_length = 2000
    config.ai.temperature = 0.2  # More focused responses
    
    # Validate configuration
    errors = config.validate()
    if errors:
        print(f"Configuration errors: {errors}")
        return
    
    # Create agent with custom config
    agent = WebSearchAgent(config.search)
    
    question = "What are the most promising quantum computing applications in healthcare?"
    
    result = agent.search_and_answer(question, "deep")
    
    print(f"Question: {question}")
    print(f"Answer: {result['answer']}")
    print(f"\nSources used:")
    for i, source in enumerate(result['sources'], 1):
        print(f"{i}. {source['title']} (Score: {source['relevance_score']:.2f})")
        print(f"   URL: {source['url']}")

def demo_batch_processing():
    """Demonstrate batch processing of multiple questions"""
    print("\n=== BATCH PROCESSING DEMO ===\n")
    
    questions = [
        "What is the current inflation rate in the United States?",
        "How do solar panels work?",
        "What are the health benefits of meditation?",
        "What is the latest news about space exploration?"
    ]
    
    print(f"Processing {len(questions)} questions...")
    
    start_time = time.time()
    results = search_multiple_questions(questions, "standard")
    end_time = time.time()
    
    print(f"Total processing time: {end_time - start_time:.2f} seconds")
    print(f"Average time per question: {(end_time - start_time) / len(questions):.2f} seconds")
    
    for i, result in enumerate(results, 1):
        print(f"\n{i}. {result['question']}")
        print(f"   Answer: {result['answer'][:150]}...")
        print(f"   Sources: {len(result['sources'])}")

def demo_search_suggestions():
    """Demonstrate search suggestion generation"""
    print("\n=== SEARCH SUGGESTIONS DEMO ===\n")
    
    topics = ["blockchain", "climate change", "machine learning", "renewable energy"]
    
    for topic in topics:
        suggestions = get_search_suggestions(topic)
        print(f"Topic: {topic}")
        print("Suggested searches:")
        for i, suggestion in enumerate(suggestions[:5], 1):
            print(f"  {i}. {suggestion}")
        print()

def interactive_search():
    """Interactive search session"""
    print("\n=== INTERACTIVE SEARCH ===\n")
    print("Enter your questions (type 'quit' to exit):")
    
    while True:
        question = input("\nYour question: ").strip()
        
        if question.lower() in ['quit', 'exit', 'q']:
            break
        
        if not question:
            continue
        
        # Check if answerable
        answerable, reason = is_question_answerable(question)
        if not answerable:
            print(f"Cannot answer: {reason}")
            continue
        
        # Choose search depth
        depth = input("Search depth (quick/standard/deep) [standard]: ").strip().lower()
        if depth not in ['quick', 'standard', 'deep']:
            depth = 'standard'
        
        try:
            print("Searching...")
            result = perform_web_search_with_ai(question, depth)
            
            print(f"\nAnswer: {result['answer']}")
            print(f"\nSources ({len(result['sources'])}):")
            for i, source in enumerate(result['sources'][:3], 1):
                print(f"{i}. {source['title']}")
                print(f"   {source['url']}")
            
        except Exception as e:
            print(f"Error: {e}")

def main():
    """Main demonstration function"""
    # Setup logging
    config = WebSearchSystemConfig()
    setup_logging(config.logging)
    
    print("Web Search Agent with AI-Powered Answers")
    print("=" * 50)
    
    # Run demonstrations
    try:
        # demo_basic_search()
        # demo_search_depths()
        # demo_query_optimization()
        # demo_content_analysis()
        # demo_advanced_search()
        # demo_batch_processing()
        # demo_search_suggestions()
        
        # Ask if user wants interactive mode
        if input("\nWould you like to try interactive search? (y/n): ").lower().startswith('y'):
            interactive_search()
        
    except KeyboardInterrupt:
        print("\nDemo interrupted by user.")
    except Exception as e:
        print(f"Demo error: {e}")
    
    print("\nDemo completed!")

if __name__ == "__main__":
    main()
