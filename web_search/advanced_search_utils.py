"""
Advanced utilities for web search and content analysis
"""

import re
import json
import hashlib
from typing import List, Dict, Any, Set, Tuple
from datetime import datetime, timedelta
from collections import Counter
import logging

logger = logging.getLogger(__name__)

class SearchQueryOptimizer:
    """Optimize search queries for better results"""
    
    def __init__(self):
        self.stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 
            'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being',
            'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could',
            'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those'
        }
    
    def optimize_query(self, query: str) -> str:
        """Optimize a search query for better results"""
        # Remove extra whitespace
        query = ' '.join(query.split())
        
        # Add quotes around phrases
        if len(query.split()) > 3 and '"' not in query:
            # Find potential phrases (2-3 consecutive important words)
            words = query.split()
            important_words = [w for w in words if w.lower() not in self.stop_words]
            
            if len(important_words) >= 2:
                # Quote the first important phrase
                phrase_start = words.index(important_words[0])
                phrase_end = min(phrase_start + 2, len(words))
                phrase = ' '.join(words[phrase_start:phrase_end + 1])
                remaining = ' '.join(words[:phrase_start] + words[phrase_end + 1:])
                query = f'"{phrase}" {remaining}'.strip()
        
        return query
    
    def generate_related_queries(self, original_query: str) -> List[str]:
        """Generate related search queries"""
        queries = []
        
        # Add temporal variations
        queries.extend([
            f"{original_query} 2024",
            f"{original_query} latest",
            f"{original_query} recent",
            f"{original_query} current"
        ])
        
        # Add question variations
        if not any(word in original_query.lower() for word in ['what', 'how', 'why', 'when', 'where', 'who']):
            queries.extend([
                f"what is {original_query}",
                f"how to {original_query}",
                f"why {original_query}"
            ])
        
        # Add context variations
        queries.extend([
            f"{original_query} explained",
            f"{original_query} guide",
            f"{original_query} overview",
            f"{original_query} analysis"
        ])
        
        return queries[:8]  # Limit to 8 variations

class ContentAnalyzer:
    """Analyze and extract insights from web content"""
    
    def __init__(self):
        self.entity_patterns = {
            'date': r'\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b|\b\d{4}[/-]\d{1,2}[/-]\d{1,2}\b',
            'year': r'\b(19|20)\d{2}\b',
            'percentage': r'\b\d+\.?\d*%\b',
            'money': r'\$\d+(?:,\d{3})*(?:\.\d{2})?|\b\d+(?:,\d{3})*(?:\.\d{2})?\s*(?:dollars?|USD|euros?|EUR)\b',
            'number': r'\b\d+(?:,\d{3})*(?:\.\d+)?\b'
        }
    
    def extract_key_facts(self, content: str) -> Dict[str, List[str]]:
        """Extract key facts and entities from content"""
        facts = {}
        
        for entity_type, pattern in self.entity_patterns.items():
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                facts[entity_type] = list(set(matches))  # Remove duplicates
        
        return facts
    
    def get_content_summary(self, content: str, max_sentences: int = 3) -> str:
        """Generate a summary of the content"""
        sentences = re.split(r'[.!?]+', content)
        sentences = [s.strip() for s in sentences if len(s.strip()) > 20]
        
        if not sentences:
            return content[:200] + "..." if len(content) > 200 else content
        
        # Score sentences based on length and position
        scored_sentences = []
        for i, sentence in enumerate(sentences):
            score = len(sentence.split())  # Word count
            if i < 3:  # Boost early sentences
                score *= 1.5
            scored_sentences.append((score, sentence))
        
        # Get top sentences
        scored_sentences.sort(reverse=True)
        top_sentences = [s[1] for s in scored_sentences[:max_sentences]]
        
        return '. '.join(top_sentences) + '.'
    
    def calculate_content_quality(self, content: str) -> float:
        """Calculate a quality score for content (0-1)"""
        if not content or len(content) < 50:
            return 0.0
        
        score = 0.0
        
        # Length score (optimal around 1000-3000 chars)
        length = len(content)
        if 1000 <= length <= 3000:
            score += 0.3
        elif 500 <= length < 1000 or 3000 < length <= 5000:
            score += 0.2
        elif length > 100:
            score += 0.1
        
        # Sentence structure score
        sentences = re.split(r'[.!?]+', content)
        avg_sentence_length = sum(len(s.split()) for s in sentences) / max(len(sentences), 1)
        if 10 <= avg_sentence_length <= 25:
            score += 0.2
        elif 5 <= avg_sentence_length < 10 or 25 < avg_sentence_length <= 35:
            score += 0.1
        
        # Information density (presence of numbers, dates, etc.)
        info_indicators = len(re.findall(r'\b\d+\b|\b(19|20)\d{2}\b|\b\d+%\b', content))
        if info_indicators > 5:
            score += 0.2
        elif info_indicators > 2:
            score += 0.1
        
        # Readability (avoid too much technical jargon)
        words = content.split()
        long_words = [w for w in words if len(w) > 12]
        if len(long_words) / len(words) < 0.1:
            score += 0.2
        elif len(long_words) / len(words) < 0.2:
            score += 0.1
        
        # Structure indicators (headings, lists, etc.)
        structure_indicators = content.count('\n') + content.count(':') + content.count('-')
        if structure_indicators > 10:
            score += 0.1
        
        return min(score, 1.0)

class SearchResultRanker:
    """Advanced ranking of search results"""
    
    def __init__(self):
        self.domain_authority = {
            'wikipedia.org': 0.9,
            'britannica.com': 0.85,
            'reuters.com': 0.8,
            'bbc.com': 0.8,
            'cnn.com': 0.75,
            'nytimes.com': 0.8,
            'washingtonpost.com': 0.8,
            'nature.com': 0.9,
            'science.org': 0.9,
            'ieee.org': 0.85,
            'gov': 0.85,  # Government domains
            'edu': 0.8,   # Educational domains
        }
    
    def calculate_authority_score(self, url: str) -> float:
        """Calculate domain authority score"""
        for domain, score in self.domain_authority.items():
            if domain in url.lower():
                return score
        
        # Default scoring based on domain type
        if '.gov' in url.lower():
            return 0.85
        elif '.edu' in url.lower():
            return 0.8
        elif '.org' in url.lower():
            return 0.7
        else:
            return 0.5
    
    def calculate_freshness_score(self, content: str) -> float:
        """Calculate content freshness based on dates found"""
        current_year = datetime.now().year
        years = re.findall(r'\b(20\d{2})\b', content)
        
        if not years:
            return 0.5  # Neutral if no dates found
        
        years = [int(y) for y in years]
        latest_year = max(years)
        
        if latest_year >= current_year:
            return 1.0
        elif latest_year >= current_year - 1:
            return 0.8
        elif latest_year >= current_year - 2:
            return 0.6
        elif latest_year >= current_year - 5:
            return 0.4
        else:
            return 0.2
    
    def rank_results(self, results: List[Dict], query: str) -> List[Dict]:
        """Rank results using multiple factors"""
        analyzer = ContentAnalyzer()
        
        for result in results:
            score = 0.0
            
            # Relevance score (keyword matching)
            relevance = self._calculate_relevance(result, query)
            score += relevance * 0.4
            
            # Authority score
            authority = self.calculate_authority_score(result.get('url', ''))
            score += authority * 0.3
            
            # Content quality score
            content = result.get('content', '')
            quality = analyzer.calculate_content_quality(content)
            score += quality * 0.2
            
            # Freshness score
            freshness = self.calculate_freshness_score(content)
            score += freshness * 0.1
            
            result['composite_score'] = score
        
        return sorted(results, key=lambda x: x.get('composite_score', 0), reverse=True)
    
    def _calculate_relevance(self, result: Dict, query: str) -> float:
        """Calculate relevance score based on keyword matching"""
        query_words = set(query.lower().split())
        
        title = result.get('title', '').lower()
        snippet = result.get('snippet', '').lower()
        content = result.get('content', '').lower()
        
        title_matches = sum(1 for word in query_words if word in title)
        snippet_matches = sum(1 for word in query_words if word in snippet)
        content_matches = sum(1 for word in query_words if word in content)
        
        # Weight title matches more heavily
        total_matches = title_matches * 3 + snippet_matches * 2 + content_matches
        max_possible = len(query_words) * 6  # 3 + 2 + 1
        
        return total_matches / max_possible if max_possible > 0 else 0

def create_search_cache_key(query: str, depth: str) -> str:
    """Create a cache key for search results"""
    content = f"{query}_{depth}_{datetime.now().strftime('%Y-%m-%d')}"
    return hashlib.md5(content.encode()).hexdigest()

def is_question_answerable(question: str) -> Tuple[bool, str]:
    """Determine if a question can be answered with web search"""
    # Questions that are too personal or subjective
    personal_indicators = ['my', 'i am', 'should i', 'what do you think']
    if any(indicator in question.lower() for indicator in personal_indicators):
        return False, "This appears to be a personal question that requires individual context."
    
    # Questions that require real-time data
    realtime_indicators = ['right now', 'currently happening', 'live', 'at this moment']
    if any(indicator in question.lower() for indicator in realtime_indicators):
        return False, "This question requires real-time information that may not be available in search results."
    
    # Questions that are too vague
    if len(question.split()) < 3:
        return False, "The question is too brief. Please provide more context for a better answer."
    
    return True, "Question appears answerable with web search."
