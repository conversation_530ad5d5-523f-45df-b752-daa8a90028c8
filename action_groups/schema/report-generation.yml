{"openapi": "3.0.0", "info": {"title": "Report Generator", "version": "1.0.0", "description": "Generate a CSV report from structured SQL data and store it in an S3 bucket"}, "paths": {"/generate-report": {"post": {"operationId": "generateReport", "description": "Takes SQL tabular data and stores it as a CSV report in the specified S3 bucket", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "array", "description": "List of rows returned from SQL query. Each row is an object with column names as keys.", "items": {"type": "object", "additionalProperties": {"oneOf": [{"type": "string"}, {"type": "number"}, {"type": "boolean"}, {"type": "null"}]}}, "example": [{"product_id": 12734732, "product_name": "test", "category": "test", "price": "120.00"}]}}, "required": ["data"]}}}}, "responses": {"200": {"description": "Report successfully generated and uploaded to S3", "content": {"application/json": {"schema": {"type": "object", "properties": {"s3_file_key": {"type": "string", "description": "Path to the generated CSV file in the S3 bucket"}, "message": {"type": "string", "description": "Confirmation message"}}, "required": ["s3_file_key", "message"]}}}}, "400": {"description": "Invalid input data"}, "500": {"description": "Error during report generation or upload"}}}}}}