import json
import psycopg2
import re
from typing import Dict, Any, List

# WARNING: Hardcoding credentials is insecure. Use Secrets Manager in production.
DB_CONFIG = {
    "host": "bedrock-poc-db.cmvkoqy0gj5v.us-east-1.rds.amazonaws.com",
    "dbname": "postgres",
    "user": "postgres",
    "password": "Bedrock#123",
    "port": 5432
}

def lambda_handler(event, context):

    print("Received event: ", event)
    try:
        # Extract and sanitize SQL query from Bedrock flow event
        raw_sql = extract_payload(event)

        # # Remove triple backticks and "sql" language label
        # sql_query = re.sub(r"^```sql\s*|```$", "", raw_sql.strip(), flags=re.IGNORECASE | re.MULTILINE).strip()
        sql_query = raw_sql['query'].strip()

        # Validate input
        if not sql_query.lower().startswith(("select", "with")):
            return {
                'statusCode': 400,
                'body': 'Only SELECT queries are allowed for security reasons.'
            }

        print("SQL Query: ", sql_query)

        # Connect to PostgreSQL
        conn = psycopg2.connect(
            host=DB_CONFIG['host'],
            dbname=DB_CONFIG['dbname'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            port=DB_CONFIG['port']
        )
        print("Conn: ", conn)

        with conn.cursor() as cur:
            cur.execute(sql_query)
            print("Cur: ", cur)


            # Fetch column names and rows
            if cur.description:
                columns = [desc[0] for desc in cur.description]
                rows = cur.fetchall()
                print("Columns: ", columns)
                print("Rows: ", rows)
                result = [dict(zip(columns, row)) for row in rows]
            else:
                result = {'message': 'Query executed successfully, no rows returned.'}
        
        conn.close()

        print("result: ", result)

        return build_response(event, result, 200)

    except Exception as e:
        print("Error: ", e)
        return generate_error_response(event, 500, str(e))


def extract_payload(event: Dict[str, Any]) -> Dict[str, Any]:
    """Extract and validate payload from event"""
    properties = event.get("requestBody", {}).get("content", {}).get("application/json", {}).get("properties", [])
    
    if not properties:
        raise ValueError("Missing or malformed 'properties' in the request body")
    
    payload = {prop['name']: prop['value'] for prop in properties}
    
    required_fields = ["query"]
    missing_fields = [field for field in required_fields if not payload.get(field)]
    
    if missing_fields:
        raise ValueError(f"Missing required fields: {', '.join(missing_fields)}")
    
    return payload

def build_response(event: Dict[str, Any], data: Any, status_code: int) -> Dict[str, Any]:
    """Build standardized API response"""
    response_body = {
        "application/json": {
            "body": json.dumps(data, separators=(',', ':'), default=str)
        }
    }
    
    action_response = {
        'actionGroup': event.get('actionGroup'),
        'apiPath': event.get('apiPath'),
        'httpMethod': event.get('httpMethod'),
        'httpStatusCode': status_code,
        'responseBody': response_body
    }

    if status_code == 400:
        action_response['responseState'] = 'REPROMPT'
    elif status_code == 500:
        action_response['responseState'] = 'ERROR'
    
    return {
        'response': action_response,
        'messageVersion': event.get('messageVersion')
    }

def generate_error_response(event: Dict[str, Any], status_code: int, message: str) -> Dict[str, Any]:
    """Generate standardized error response"""
    error_data = {"error": f"{message}. Unable to process the SQL Query"}
    return build_response(event, error_data, status_code)
